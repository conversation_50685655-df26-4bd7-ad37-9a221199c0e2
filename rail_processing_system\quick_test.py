#!/usr/bin/env python3
"""
快速测试改进后的算法
"""

import os
import sys
import numpy as np
import open3d as o3d
import time
from datetime import datetime

# 导入改进后的模块
from srs_profile_register_format_xyz import icp_registration
from srs_stitching_error import calculate_stitching_error

def create_test_data():
    """
    创建测试数据
    """
    print("创建测试数据...")
    
    # 创建两个相似但有小偏移的点云
    np.random.seed(42)
    
    # 基础点云
    n_points = 1000
    base_points = np.random.randn(n_points, 3) * 10
    base_points[:, 2] += 100  # z方向偏移到100mm附近
    
    # 目标点云（添加小的变换）
    transformation = np.eye(4)
    transformation[0, 3] = 0.5   # x方向偏移0.5mm
    transformation[1, 3] = 0.3   # y方向偏移0.3mm
    transformation[2, 3] = 0.15  # z方向偏移0.15mm（测试目标）
    
    # 添加小的旋转
    angle = 0.01  # 约0.57度
    transformation[0, 0] = np.cos(angle)
    transformation[0, 1] = -np.sin(angle)
    transformation[1, 0] = np.sin(angle)
    transformation[1, 1] = np.cos(angle)
    
    # 应用变换
    target_points_homogeneous = np.hstack([base_points, np.ones((n_points, 1))])
    target_points = (transformation @ target_points_homogeneous.T).T[:, :3]
    
    # 添加噪声
    base_points += np.random.normal(0, 0.02, base_points.shape)  # 0.02mm噪声
    target_points += np.random.normal(0, 0.02, target_points.shape)
    
    return base_points, target_points, transformation

def test_improved_registration():
    """
    测试改进的配准算法
    """
    print("=" * 60)
    print("测试改进后的点云配准算法")
    print("=" * 60)
    
    # 创建测试数据
    source_points, target_points, true_transformation = create_test_data()
    
    print(f"测试数据:")
    print(f"  源点云: {len(source_points)} 点")
    print(f"  目标点云: {len(target_points)} 点")
    print(f"  真实z方向偏移: {true_transformation[2, 3]:.3f}mm")
    
    # 创建点云对象
    source_pcd = o3d.geometry.PointCloud()
    source_pcd.points = o3d.utility.Vector3dVector(source_points)
    
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    
    # 测试原始算法
    print(f"\n--- 测试原始算法 ---")
    start_time = time.time()
    
    trans_init = np.eye(4)
    transformation_old = icp_registration(source_pcd, target_pcd, trans_init,
                                        max_correspondence_distance=0.5,
                                        use_preprocessing=False,
                                        use_multiscale=False)
    
    old_time = time.time() - start_time
    
    # 应用变换
    source_transformed_old = source_pcd.transform(transformation_old)
    
    # 计算误差
    error_stats_old = calculate_stitching_error(source_transformed_old, target_pcd,
                                              distance_threshold=1.0,
                                              remove_outliers=False)
    
    print(f"原始算法结果:")
    print(f"  耗时: {old_time:.2f}秒")
    if error_stats_old:
        print(f"  平均Z误差: {error_stats_old['mean_z_error']:.4f}mm")
        print(f"  Z误差标准差: {error_stats_old['std_z_error']:.4f}mm")
        print(f"  匹配点对: {error_stats_old['matched_pairs']}")
    
    # 测试改进算法
    print(f"\n--- 测试改进算法 ---")
    start_time = time.time()
    
    # 重新创建源点云（因为之前被变换了）
    source_pcd = o3d.geometry.PointCloud()
    source_pcd.points = o3d.utility.Vector3dVector(source_points)
    
    transformation_new = icp_registration(source_pcd, target_pcd, trans_init,
                                        max_correspondence_distance=0.03,
                                        use_preprocessing=True,
                                        use_multiscale=True)
    
    new_time = time.time() - start_time
    
    # 应用变换
    source_transformed_new = source_pcd.transform(transformation_new)
    
    # 计算误差
    error_stats_new = calculate_stitching_error(source_transformed_new, target_pcd,
                                              distance_threshold=0.3,
                                              remove_outliers=True,
                                              outlier_method='iqr')
    
    print(f"改进算法结果:")
    print(f"  耗时: {new_time:.2f}秒")
    if error_stats_new:
        print(f"  平均Z误差: {error_stats_new['mean_z_error']:.4f}mm")
        print(f"  Z误差标准差: {error_stats_new['std_z_error']:.4f}mm")
        print(f"  匹配点对: {error_stats_new['matched_pairs']}")
        if error_stats_new.get('outliers_removed', 0) > 0:
            print(f"  移除异常值: {error_stats_new['outliers_removed']}")
        print(f"  Z误差<0.1mm占比: {error_stats_new.get('z_error_under_0_1_ratio', 0)*100:.1f}%")
    
    # 比较结果
    print(f"\n--- 改进效果对比 ---")
    if error_stats_old and error_stats_new:
        mean_improvement = error_stats_old['mean_z_error'] - error_stats_new['mean_z_error']
        std_improvement = error_stats_old['std_z_error'] - error_stats_new['std_z_error']
        
        print(f"平均Z误差改进: {mean_improvement:.4f}mm ({mean_improvement/error_stats_old['mean_z_error']*100:+.1f}%)")
        print(f"标准差改进: {std_improvement:.4f}mm ({std_improvement/error_stats_old['std_z_error']*100:+.1f}%)")
        
        # 检查是否达到目标
        target_achieved = (error_stats_new['mean_z_error'] <= 0.1 and 
                          error_stats_new['std_z_error'] <= 0.1)
        
        print(f"\n目标达成情况:")
        print(f"  平均误差 ≤ 0.1mm: {'✓' if error_stats_new['mean_z_error'] <= 0.1 else '✗'}")
        print(f"  标准差 ≤ 0.1mm: {'✓' if error_stats_new['std_z_error'] <= 0.1 else '✗'}")
        print(f"  整体目标: {'🎉 达成!' if target_achieved else '❌ 未达成'}")
        
        return target_achieved
    
    return False

def main():
    """
    主函数
    """
    print(f"测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        success = test_improved_registration()
        
        print(f"\n测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        if success:
            print("\n✅ 算法改进成功！z方向误差已降低到0.1mm以内")
        else:
            print("\n⚠️  算法仍需进一步优化")
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
