#!/usr/bin/env python3
"""
处理真实的rail数据，验证改进后的算法效果
"""

import os
import sys
import numpy as np
import open3d as o3d
import time
from datetime import datetime

# 导入改进后的模块
from srs_profile_register_format_xyz import icp_registration
from srs_stitching_error import calculate_stitching_error
from config import get_config

def load_xyz_data(filepath, max_points=None):
    """
    加载XYZ数据文件
    """
    print(f"加载数据文件: {filepath}")
    
    if not os.path.exists(filepath):
        raise FileNotFoundError(f"数据文件不存在: {filepath}")
    
    # 读取数据
    data = np.loadtxt(filepath)
    
    # 提取XYZ坐标（忽略第4列）
    points = data[:, :3]
    
    print(f"  原始点数: {len(points):,}")
    
    # 如果指定了最大点数，进行随机采样
    if max_points and len(points) > max_points:
        indices = np.random.choice(len(points), max_points, replace=False)
        points = points[indices]
        print(f"  随机采样后: {len(points):,} 点")
    
    return points

def process_point_cloud_pair(source_points, target_points, pair_name, output_dir):
    """
    处理一对点云的配准和误差分析
    """
    print(f"\n{'='*60}")
    print(f"处理点云对: {pair_name}")
    print(f"{'='*60}")
    
    # 创建点云对象
    source_pcd = o3d.geometry.PointCloud()
    source_pcd.points = o3d.utility.Vector3dVector(source_points)
    
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    
    print(f"源点云: {len(source_points):,} 点")
    print(f"目标点云: {len(target_points):,} 点")
    
    # 执行改进的配准
    print(f"\n--- 执行改进的ICP配准 ---")
    start_time = time.time()
    
    trans_init = np.eye(4)
    transformation = icp_registration(source_pcd, target_pcd, trans_init,
                                    max_correspondence_distance=0.03,  # 严格阈值
                                    use_preprocessing=True,
                                    use_multiscale=True)
    
    registration_time = time.time() - start_time
    print(f"配准完成，耗时: {registration_time:.1f}秒")
    
    # 应用变换
    source_transformed = source_pcd.transform(transformation)
    
    # 保存变换矩阵
    matrix_file = os.path.join(output_dir, f"{pair_name}_transformation_matrix.txt")
    np.savetxt(matrix_file, transformation, fmt='%.8f')
    print(f"变换矩阵已保存: {matrix_file}")
    
    # 计算拼接误差
    print(f"\n--- 计算拼接误差 ---")
    start_time = time.time()
    
    error_file = os.path.join(output_dir, f"{pair_name}_stitching_error.txt")
    error_stats = calculate_stitching_error(source_transformed, target_pcd,
                                          distance_threshold=0.3,  # 严格阈值
                                          output_file=error_file,
                                          remove_outliers=True,
                                          outlier_method='iqr')
    
    error_time = time.time() - start_time
    print(f"误差分析完成，耗时: {error_time:.1f}秒")
    
    return {
        'transformation': transformation,
        'registration_time': registration_time,
        'error_time': error_time,
        'error_stats': error_stats
    }

def analyze_results(results):
    """
    分析所有配准结果
    """
    print(f"\n{'='*80}")
    print("最终结果分析")
    print(f"{'='*80}")
    
    target_mean_error = 0.1  # 目标平均误差
    target_std_error = 0.1   # 目标标准差
    
    all_meet_target = True
    
    for pair_name, result in results.items():
        if 'error_stats' not in result or not result['error_stats']:
            continue
            
        stats = result['error_stats']
        
        print(f"\n{pair_name.upper().replace('_', ' vs ')}:")
        print("-" * 50)
        
        # 基本统计
        print(f"处理时间:")
        print(f"  配准耗时: {result['registration_time']:.1f}秒")
        print(f"  误差分析: {result['error_time']:.1f}秒")
        
        # 匹配统计
        print(f"匹配统计:")
        print(f"  原始匹配点对: {stats.get('original_pairs', 'N/A'):,}")
        if stats.get('outliers_removed', 0) > 0:
            print(f"  移除异常值: {stats['outliers_removed']:,} ({stats['outliers_removed']/stats['original_pairs']*100:.1f}%)")
        print(f"  有效匹配点对: {stats.get('matched_pairs', 'N/A'):,}")
        
        # Z方向误差
        mean_z_error = stats.get('mean_z_error', float('inf'))
        std_z_error = stats.get('std_z_error', float('inf'))
        
        print(f"Z方向误差:")
        print(f"  平均误差: {mean_z_error:.4f} mm")
        print(f"  标准差: {std_z_error:.4f} mm")
        print(f"  最大误差: {stats.get('max_z_error', 'N/A'):.4f} mm")
        print(f"  最小误差: {stats.get('min_z_error', 'N/A'):.4f} mm")
        
        # 精度统计
        print(f"精度统计:")
        print(f"  Z误差<0.1mm占比: {stats.get('z_error_under_0_1_ratio', 0)*100:.1f}%")
        print(f"  Z误差<0.3mm占比: {stats.get('z_error_under_0_3_ratio', 0)*100:.1f}%")
        print(f"  平均距离: {stats.get('mean_distance', 'N/A'):.4f} mm")
        
        # 目标达成检查
        mean_meets_target = mean_z_error <= target_mean_error
        std_meets_target = std_z_error <= target_std_error
        
        print(f"目标达成:")
        print(f"  平均误差 ≤ {target_mean_error}mm: {'✓' if mean_meets_target else '✗'} ({mean_z_error:.4f}mm)")
        print(f"  标准差 ≤ {target_std_error}mm: {'✓' if std_meets_target else '✗'} ({std_z_error:.4f}mm)")
        
        if not (mean_meets_target and std_meets_target):
            all_meet_target = False
    
    # 总体结论
    print(f"\n{'='*60}")
    print("总体评估")
    print(f"{'='*60}")
    
    if all_meet_target:
        print("🎉 恭喜！所有配准对的z方向误差都达到了目标要求！")
        print(f"✓ 平均误差和标准差都控制在{target_mean_error}mm以内")
        print("✓ 改进算法在真实数据上验证成功")
    else:
        print("⚠️  部分配准对未完全达到目标要求")
        print("💡 建议：可能需要针对真实数据特征进一步调整参数")
    
    return all_meet_target

def main():
    """
    主处理函数
    """
    print(f"开始处理真实rail数据")
    print(f"处理开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 配置
    config = get_config()
    rail_dir = config['data']['rail_data_dir']
    output_dir = config['data']['output_dir']
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 设置最大点数以控制处理时间
    max_points_per_cloud = 50000  # 每个点云最多5万点
    
    try:
        # 加载数据
        print(f"\n加载点云数据...")
        blue_points = load_xyz_data(os.path.join(rail_dir, 'blue.xyz'), max_points_per_cloud)
        green_points = load_xyz_data(os.path.join(rail_dir, 'green.xyz'), max_points_per_cloud)
        red_points = load_xyz_data(os.path.join(rail_dir, 'red.xyz'), max_points_per_cloud)
        
        # 处理三对点云
        results = {}
        
        # Blue vs Green
        results['blue_vs_green'] = process_point_cloud_pair(
            blue_points, green_points, 'blue_vs_green', output_dir)
        
        # Blue vs Red  
        results['blue_vs_red'] = process_point_cloud_pair(
            blue_points, red_points, 'blue_vs_red', output_dir)
        
        # Green vs Red
        results['green_vs_red'] = process_point_cloud_pair(
            green_points, red_points, 'green_vs_red', output_dir)
        
        # 分析结果
        success = analyze_results(results)
        
        print(f"\n处理完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"结果文件保存在: {output_dir}")
        
        return 0 if success else 1
        
    except Exception as e:
        print(f"处理失败: {e}")
        import traceback
        traceback.print_exc()
        return 1

if __name__ == "__main__":
    sys.exit(main())
