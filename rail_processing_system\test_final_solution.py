#!/usr/bin/env python3
"""
最终解决方案测试：自适应增强匹配策略
验证无异常值移除的高精度配准效果
"""

import os
import sys
import time
import numpy as np
import open3d as o3d
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from srs_stitching_error import calculate_stitching_error
from srs_profile_register_format_xyz import icp_registration

def load_xyz_data(filename, max_points=None):
    """
    加载XYZ数据
    """
    print(f"加载数据: {filename}")
    points = np.loadtxt(filename)
    
    if points.shape[1] > 3:
        points = points[:, :3]
    
    if max_points and len(points) > max_points:
        indices = np.random.choice(len(points), max_points, replace=False)
        points = points[indices]
    
    print(f"  加载点数: {len(points):,}")
    return points

def filter_by_z_range(points, z_range):
    """
    按Z范围过滤点云
    """
    z_min, z_max = z_range
    z_mask = (points[:, 2] >= z_min) & (points[:, 2] <= z_max)
    return points[z_mask]

def test_final_solution():
    """
    测试最终解决方案
    """
    print("=" * 80)
    print("最终解决方案：自适应增强匹配策略测试")
    print("无异常值移除的高精度点云配准")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 数据路径
    rail_dir = 'rail'
    blue_file = os.path.join(rail_dir, 'blue.xyz')
    green_file = os.path.join(rail_dir, 'green.xyz')
    red_file = os.path.join(rail_dir, 'red.xyz')
    
    # 检查文件是否存在
    files = [blue_file, green_file, red_file]
    for file in files:
        if not os.path.exists(file):
            print(f"❌ 数据文件不存在: {file}")
            return
    
    # 加载数据
    print("\n📁 加载数据...")
    max_points = 80000  # 增加采样点数
    blue_points = load_xyz_data(blue_file, max_points)
    green_points = load_xyz_data(green_file, max_points)
    red_points = load_xyz_data(red_file, max_points)
    
    # 确定公共Z范围
    blue_z_range = [blue_points[:, 2].min(), blue_points[:, 2].max()]
    green_z_range = [green_points[:, 2].min(), green_points[:, 2].max()]
    red_z_range = [red_points[:, 2].min(), red_points[:, 2].max()]
    
    common_z_min = max(blue_z_range[0], green_z_range[0], red_z_range[0])
    common_z_max = min(blue_z_range[1], green_z_range[1], red_z_range[1])
    common_z_range = [common_z_min, common_z_max]
    
    print(f"公共Z范围: [{common_z_min:.2f}, {common_z_max:.2f}]")
    
    # 过滤数据
    blue_filtered = filter_by_z_range(blue_points, common_z_range)
    green_filtered = filter_by_z_range(green_points, common_z_range)
    red_filtered = filter_by_z_range(red_points, common_z_range)
    
    print(f"过滤后点数 - Blue: {len(blue_filtered):,}, Green: {len(green_filtered):,}, Red: {len(red_filtered):,}")
    
    # 定义测试对
    test_pairs = [
        ('blue', blue_filtered, 'green', green_filtered),
        ('blue', blue_filtered, 'red', red_filtered),
        ('green', green_filtered, 'red', red_filtered)
    ]
    
    results = {}
    
    for source_name, source_points, target_name, target_points in test_pairs:
        pair_name = f"{source_name}_vs_{target_name}"
        print(f"\n{'='*60}")
        print(f"测试点云对: {pair_name}")
        print(f"{'='*60}")
        
        # 创建点云对象
        source_pcd = o3d.geometry.PointCloud()
        source_pcd.points = o3d.utility.Vector3dVector(source_points)
        
        target_pcd = o3d.geometry.PointCloud()
        target_pcd.points = o3d.utility.Vector3dVector(target_points)
        
        print(f"源点云: {len(source_points):,} 点")
        print(f"目标点云: {len(target_points):,} 点")
        
        # 执行配准
        print(f"\n🔧 执行优化ICP配准...")
        trans_init = np.eye(4)
        start_time = time.time()
        
        transformation = icp_registration(source_pcd, target_pcd, trans_init,
                                        max_correspondence_distance=0.05,  # 放宽阈值
                                        use_preprocessing=True,
                                        use_multiscale=True)
        
        registration_time = time.time() - start_time
        print(f"配准完成，耗时: {registration_time:.2f}秒")
        
        # 应用变换
        source_transformed = source_pcd.transform(transformation)
        
        # 测试1: 传统方法（异常值移除）
        print(f"\n--- 传统方法（异常值移除）---")
        start_time = time.time()
        error_stats_traditional = calculate_stitching_error(
            source_transformed, target_pcd,
            distance_threshold=0.5,  # 放宽阈值
            remove_outliers=True,
            outlier_method='iqr',
            use_enhanced_matching=False
        )
        traditional_time = time.time() - start_time
        
        # 测试2: 自适应增强匹配策略
        print(f"\n--- 自适应增强匹配策略（无异常值移除）---")
        start_time = time.time()
        error_stats_adaptive = calculate_stitching_error(
            source_transformed, target_pcd,
            distance_threshold=0.5,  # 放宽阈值
            remove_outliers=False,
            outlier_method='iqr',
            use_enhanced_matching=True,
            adaptive_matching=True  # 启用自适应匹配
        )
        adaptive_time = time.time() - start_time
        
        # 分析结果
        def analyze_result(name, stats, processing_time):
            if stats:
                mean_error = stats['mean_z_error']
                std_error = stats['std_z_error']
                target_achieved = (mean_error <= 0.1 and std_error <= 0.1)
                
                print(f"\n{name}:")
                print(f"  处理时间: {processing_time:.3f}秒")
                print(f"  匹配点对: {stats['matched_pairs']:,}")
                print(f"  平均Z误差: {mean_error:.4f} mm")
                print(f"  Z误差标准差: {std_error:.4f} mm")
                print(f"  Z误差<0.1mm占比: {stats['z_error_under_0_1_ratio']:.1%}")
                print(f"  目标达成: {'✅' if target_achieved else '❌'}")
                
                return target_achieved, mean_error, std_error
            else:
                print(f"\n{name}: ❌ 计算失败")
                return False, float('inf'), float('inf')
        
        traditional_achieved, trad_mean, trad_std = analyze_result(
            "传统方法", error_stats_traditional, traditional_time)
        adaptive_achieved, adapt_mean, adapt_std = analyze_result(
            "自适应增强匹配", error_stats_adaptive, adaptive_time)
        
        # 记录结果
        results[pair_name] = {
            'traditional_achieved': traditional_achieved,
            'adaptive_achieved': adaptive_achieved,
            'traditional_mean': trad_mean,
            'adaptive_mean': adapt_mean,
            'traditional_std': trad_std,
            'adaptive_std': adapt_std,
            'traditional_time': traditional_time,
            'adaptive_time': adaptive_time
        }
        
        # 对比分析
        print(f"\n📊 对比分析:")
        if adaptive_achieved and traditional_achieved:
            mean_improvement = ((trad_mean - adapt_mean) / trad_mean * 100) if trad_mean > 0 else 0
            std_improvement = ((trad_std - adapt_std) / trad_std * 100) if trad_std > 0 else 0
            time_ratio = adaptive_time / traditional_time if traditional_time > 0 else 1
            
            print(f"  平均误差改善: {mean_improvement:+.1f}%")
            print(f"  标准差改善: {std_improvement:+.1f}%")
            print(f"  处理时间比: {time_ratio:.2f}x")
            
            if adaptive_achieved:
                print(f"  🎉 自适应增强匹配策略成功！")
        elif adaptive_achieved:
            print(f"  🏆 只有自适应增强匹配策略达到目标！")
        elif traditional_achieved:
            print(f"  ⚠️  只有传统方法达到目标")
        else:
            print(f"  ❌ 两种方法都未达到目标")
    
    # 总结报告
    print(f"\n{'='*80}")
    print("🎯 最终测试总结")
    print(f"{'='*80}")
    
    traditional_success = sum(1 for r in results.values() if r['traditional_achieved'])
    adaptive_success = sum(1 for r in results.values() if r['adaptive_achieved'])
    
    print(f"传统方法成功率: {traditional_success}/3 ({traditional_success/3*100:.1f}%)")
    print(f"自适应增强匹配成功率: {adaptive_success}/3 ({adaptive_success/3*100:.1f}%)")
    
    if adaptive_success >= traditional_success:
        print(f"\n🏆 结论：自适应增强匹配策略可以成功替代异常值移除！")
        print(f"✅ 优势：")
        print(f"  - 无需异常值检测参数调优")
        print(f"  - 更稳定和可预测的结果")
        print(f"  - 自适应调整匹配参数")
        print(f"  - 保持或提高配准精度")
    else:
        print(f"\n⚠️  结论：自适应增强匹配策略需要进一步优化")
        print(f"💡 建议：")
        print(f"  - 进一步调整自适应参数")
        print(f"  - 考虑结合轻度异常值过滤")
        print(f"  - 针对特定点云对优化策略")

if __name__ == "__main__":
    test_final_solution()
