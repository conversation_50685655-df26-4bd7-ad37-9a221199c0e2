#!/usr/bin/env python3
"""
Rail数据处理系统配置文件
"""

# 数据路径配置
DATA_CONFIG = {
    'rail_data_dir': 'rail',
    'output_dir': 'rail_results',
    'data_files': {
        'blue': 'blue.xyz',
        'green': 'green.xyz',
        'red': 'red.xyz'
    }
}

# 处理参数配置
PROCESSING_CONFIG = {
    # 采样参数
    'max_points_per_cloud': 50000,  # 每个点云的最大点数
    'sampling_method': 'random',    # 采样方法: 'random', 'voxel', 'uniform'
    
    # Z值滤波参数
    'use_common_z_range': True,     # 是否使用公共Z范围
    'z_range_method': 'intersection',  # Z范围确定方法: 'intersection', 'union', 'manual'
    'manual_z_range': None,         # 手动指定Z范围 [z_min, z_max]
    
    # ICP配准参数（优化后）
    'icp_max_correspondence_distance': 0.03,  # ICP对应距离阈值 (mm) - 更严格
    'icp_relative_fitness': 1e-6,            # ICP收敛阈值 (fitness) - 更严格
    'icp_relative_rmse': 1e-6,               # ICP收敛阈值 (RMSE) - 更严格
    'icp_max_iteration': 200,                # ICP最大迭代次数 - 增加

    # 拼接误差分析参数（优化后）
    'distance_threshold': 0.3,       # 距离阈值 (mm) - 更严格
    'error_percentiles': [25, 50, 75, 95],  # 误差百分位数
    'remove_outliers': True,         # 启用异常值过滤
    'outlier_method': 'iqr',         # 异常值检测方法
    'use_enhanced_matching': False,  # 使用增强匹配策略（替代异常值移除）
}

# 质量评估配置
QUALITY_CONFIG = {
    # 质量分数权重
    'high_precision_weight': 0.6,   # 高精度点占比权重
    'low_error_weight': 0.4,        # 低误差占比权重
    
    # 精度阈值（优化后）
    'high_precision_threshold': 0.1,  # 高精度阈值 (mm) - 更严格
    'medium_precision_threshold': 0.3,  # 中精度阈值 (mm) - 更严格
    'low_error_threshold': 0.1,      # 低误差阈值 (mm) - 更严格
}

# 可视化配置
VISUALIZATION_CONFIG = {
    'figure_size': (15, 10),        # 图表尺寸
    'dpi': 300,                     # 图表分辨率
    'save_format': 'png',           # 保存格式
    'show_plots': False,            # 是否显示图表
    'font_size': 12,                # 字体大小
}

# 输出配置
OUTPUT_CONFIG = {
    'save_transformation_matrix': True,     # 保存变换矩阵
    'save_registration_stats': True,       # 保存配准统计
    'save_stitching_error': True,          # 保存拼接误差
    'save_visualization': True,            # 保存可视化图表
    'generate_summary_report': True,       # 生成总结报告
    
    # 文件命名
    'filename_suffix': '',                 # 文件名后缀
    'timestamp_in_filename': False,        # 文件名中包含时间戳
}

# 系统配置
SYSTEM_CONFIG = {
    'max_processing_time': 600,     # 最大处理时间 (秒)
    'memory_limit_gb': 8,           # 内存限制 (GB)
    'parallel_processing': False,   # 并行处理
    'verbose': True,                # 详细输出
    'log_level': 'INFO',           # 日志级别
}

# 算法配置（优化后）
ALGORITHM_CONFIG = {
    # 预处理（启用）
    'remove_outliers': True,        # 去除离群点 - 启用
    'outlier_nb_points': 20,        # 离群点检测邻居数
    'outlier_std_ratio': 2.0,       # 离群点标准差比率

    # 滤波（优化）
    'voxel_size': 0.02,            # 体素滤波大小 (mm) - 更精细
    'statistical_filter': True,    # 统计滤波 - 启用
    'radius_filter': False,        # 半径滤波

    # 配准（增强）
    'use_manual_registration': False,     # 使用手动配准
    'manual_points_count': 4,             # 手动配准点数
    'registration_method': 'multi_scale_icp',  # 配准方法: 'icp', 'multi_scale_icp', 'colored_icp'
    'use_preprocessing': True,            # 启用预处理
    'use_multiscale': True,              # 启用多尺度配准
    'use_z_constraint': True,            # 启用z方向约束优化
}

def get_config():
    """
    获取完整配置
    """
    return {
        'data': DATA_CONFIG,
        'processing': PROCESSING_CONFIG,
        'quality': QUALITY_CONFIG,
        'visualization': VISUALIZATION_CONFIG,
        'output': OUTPUT_CONFIG,
        'system': SYSTEM_CONFIG,
        'algorithm': ALGORITHM_CONFIG
    }

def print_config():
    """
    打印当前配置
    """
    config = get_config()
    
    print("=" * 60)
    print("Rail数据处理系统配置")
    print("=" * 60)
    
    for section_name, section_config in config.items():
        print(f"\n[{section_name.upper()}]")
        print("-" * 30)
        for key, value in section_config.items():
            print(f"{key}: {value}")

def validate_config():
    """
    验证配置有效性
    """
    config = get_config()
    errors = []
    
    # 验证数据配置
    if not config['data']['rail_data_dir']:
        errors.append("数据目录未配置")
    
    # 验证处理参数
    if config['processing']['max_points_per_cloud'] <= 0:
        errors.append("最大点数必须大于0")
    
    if config['processing']['icp_max_correspondence_distance'] <= 0:
        errors.append("ICP对应距离阈值必须大于0")
    
    # 验证质量配置
    weights_sum = (config['quality']['high_precision_weight'] + 
                   config['quality']['low_error_weight'])
    if abs(weights_sum - 1.0) > 1e-6:
        errors.append(f"质量权重之和必须为1.0，当前为{weights_sum}")
    
    return errors

if __name__ == "__main__":
    # 打印配置
    print_config()
    
    # 验证配置
    errors = validate_config()
    if errors:
        print("\n❌ 配置验证失败:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("\n✅ 配置验证通过")
