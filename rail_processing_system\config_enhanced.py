#!/usr/bin/env python3
"""
增强匹配策略配置文件
专门用于测试无异常值移除的高精度配准
"""

# 数据路径配置
DATA_CONFIG = {
    'rail_data_dir': 'rail',
    'output_dir': 'rail_results_enhanced',
    'data_files': {
        'blue': 'blue.xyz',
        'green': 'green.xyz',
        'red': 'red.xyz'
    }
}

# 处理参数配置（增强版）
PROCESSING_CONFIG = {
    # 采样参数
    'max_points_per_cloud': 50000,  # 每个点云的最大点数
    'sampling_method': 'random',    # 采样方法: 'random', 'voxel', 'uniform'
    
    # Z值滤波参数
    'use_common_z_range': True,     # 是否使用公共Z范围
    'z_range_method': 'intersection',  # Z范围确定方法: 'intersection', 'union', 'manual'
    'manual_z_range': None,         # 手动指定Z范围 [z_min, z_max]
    
    # ICP配准参数（进一步优化）
    'icp_max_correspondence_distance': 0.02,  # ICP对应距离阈值 (mm) - 更严格
    'icp_relative_fitness': 1e-7,            # ICP收敛阈值 (fitness) - 更严格
    'icp_relative_rmse': 1e-7,               # ICP收敛阈值 (RMSE) - 更严格
    'icp_max_iteration': 300,                # ICP最大迭代次数 - 进一步增加

    # 拼接误差分析参数（增强版）
    'distance_threshold': 0.25,      # 距离阈值 (mm) - 更严格
    'error_percentiles': [25, 50, 75, 95],  # 误差百分位数
    'remove_outliers': False,        # 禁用异常值过滤
    'outlier_method': 'iqr',         # 异常值检测方法（备用）
    'use_enhanced_matching': True,   # 启用增强匹配策略
}

# 质量评估配置（更严格）
QUALITY_CONFIG = {
    # 质量分数权重
    'high_precision_weight': 0.7,   # 高精度点占比权重 - 增加
    'low_error_weight': 0.3,        # 低误差占比权重 - 减少
    
    # 精度阈值（更严格）
    'high_precision_threshold': 0.05,  # 高精度阈值 (mm) - 更严格
    'medium_precision_threshold': 0.2,  # 中精度阈值 (mm) - 更严格
    'low_error_threshold': 0.05,       # 低误差阈值 (mm) - 更严格
}

# 可视化配置
VISUALIZATION_CONFIG = {
    'figure_size': (15, 10),        # 图表尺寸
    'dpi': 300,                     # 图表分辨率
    'save_format': 'png',           # 保存格式
    'show_plots': False,            # 是否显示图表
    'font_size': 12,                # 字体大小
}

# 输出配置
OUTPUT_CONFIG = {
    'save_transformation_matrix': True,     # 保存变换矩阵
    'save_registration_stats': True,       # 保存配准统计
    'save_stitching_error': True,          # 保存拼接误差
    'save_visualization': True,            # 保存可视化图表
    'generate_summary_report': True,       # 生成总结报告
    
    # 文件命名
    'filename_suffix': '_enhanced',        # 文件名后缀
    'timestamp_in_filename': False,        # 文件名中包含时间戳
}

# 系统配置
SYSTEM_CONFIG = {
    'max_processing_time': 900,     # 最大处理时间 (秒) - 增加
    'memory_limit_gb': 12,          # 内存限制 (GB) - 增加
    'parallel_processing': False,   # 并行处理
    'verbose': True,                # 详细输出
    'log_level': 'INFO',           # 日志级别
}

# 算法配置（增强版）
ALGORITHM_CONFIG = {
    # 预处理（增强）
    'remove_outliers': True,        # 去除离群点 - 保持启用
    'outlier_nb_points': 30,        # 离群点检测邻居数 - 增加
    'outlier_std_ratio': 1.5,       # 离群点标准差比率 - 更严格

    # 滤波（更精细）
    'voxel_size': 0.015,           # 体素滤波大小 (mm) - 更精细
    'statistical_filter': True,    # 统计滤波 - 启用
    'radius_filter': False,        # 半径滤波

    # 配准（增强）
    'use_manual_registration': False,     # 使用手动配准
    'manual_points_count': 4,             # 手动配准点数
    'registration_method': 'multi_scale_icp',  # 配准方法
    'use_preprocessing': True,            # 启用预处理
    'use_multiscale': True,              # 启用多尺度配准
    'use_z_constraint': True,            # 启用z方向约束优化
}

# 增强匹配策略配置
ENHANCED_MATCHING_CONFIG = {
    # 多尺度搜索参数
    'knn_search_count': 5,          # K近邻搜索数量
    'max_z_difference_ratio': 2.0,  # Z方向差异阈值比率
    'min_neighbor_density': 3,      # 最小邻居密度
    'density_search_radius_ratio': 1.5,  # 密度搜索半径比率
    
    # 综合得分权重
    'distance_weight': 0.4,         # 距离权重
    'z_error_weight': 0.4,          # Z误差权重
    'density_weight': 0.2,          # 密度权重
    
    # 质量控制
    'min_match_quality_score': 0.8, # 最小匹配质量得分
    'enable_geometric_consistency': True,  # 启用几何一致性检查
    'enable_local_density_filter': True,  # 启用局部密度过滤
}

def get_enhanced_config():
    """
    获取增强配置
    """
    return {
        'data': DATA_CONFIG,
        'processing': PROCESSING_CONFIG,
        'quality': QUALITY_CONFIG,
        'visualization': VISUALIZATION_CONFIG,
        'output': OUTPUT_CONFIG,
        'system': SYSTEM_CONFIG,
        'algorithm': ALGORITHM_CONFIG,
        'enhanced_matching': ENHANCED_MATCHING_CONFIG
    }

def print_enhanced_config():
    """
    打印增强配置
    """
    config = get_enhanced_config()
    
    print("=" * 60)
    print("增强匹配策略配置")
    print("=" * 60)
    
    for section_name, section_config in config.items():
        print(f"\n[{section_name.upper()}]")
        print("-" * 30)
        for key, value in section_config.items():
            print(f"{key}: {value}")

def compare_configs():
    """
    比较标准配置和增强配置的差异
    """
    from config import get_config
    
    standard_config = get_config()
    enhanced_config = get_enhanced_config()
    
    print("=" * 80)
    print("配置对比：标准配置 vs 增强配置")
    print("=" * 80)
    
    # 重点对比处理参数
    print("\n关键差异:")
    print("-" * 40)
    
    std_proc = standard_config['processing']
    enh_proc = enhanced_config['processing']
    
    print(f"异常值移除:")
    print(f"  标准配置: {std_proc['remove_outliers']}")
    print(f"  增强配置: {enh_proc['remove_outliers']}")
    
    print(f"增强匹配:")
    print(f"  标准配置: {std_proc.get('use_enhanced_matching', False)}")
    print(f"  增强配置: {enh_proc['use_enhanced_matching']}")
    
    print(f"距离阈值:")
    print(f"  标准配置: {std_proc['distance_threshold']} mm")
    print(f"  增强配置: {enh_proc['distance_threshold']} mm")
    
    print(f"ICP对应距离:")
    print(f"  标准配置: {std_proc['icp_max_correspondence_distance']} mm")
    print(f"  增强配置: {enh_proc['icp_max_correspondence_distance']} mm")

if __name__ == "__main__":
    # 打印增强配置
    print_enhanced_config()
    
    print("\n" + "="*80)
    
    # 比较配置
    compare_configs()
