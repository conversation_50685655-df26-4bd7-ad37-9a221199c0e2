#!/usr/bin/env python3
"""
运行增强匹配策略的Rail数据处理
专门测试无异常值移除的高精度配准效果
"""

import os
import sys
import time
import numpy as np
import open3d as o3d
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from srs_stitching_error import calculate_stitching_error
from srs_profile_register_format_xyz import icp_registration
from config_enhanced import get_enhanced_config

def print_header():
    """
    打印系统标题
    """
    print("=" * 80)
    print("增强匹配策略Rail数据处理系统")
    print("无异常值移除的高精度点云配准测试")
    print("=" * 80)
    print(f"启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("版本: Enhanced 1.0")
    print("=" * 80)

def load_and_filter_pointcloud(filename, z_range=None, max_points=None):
    """
    加载并过滤点云数据
    """
    print(f"加载点云: {filename}")
    
    # 加载点云数据
    points = np.loadtxt(filename)
    print(f"  原始点数: {len(points):,}")
    
    # 如果数据有4列，只取前3列(XYZ)
    if points.shape[1] > 3:
        points = points[:, :3]
    
    # Z值过滤
    if z_range:
        z_min, z_max = z_range
        z_mask = (points[:, 2] >= z_min) & (points[:, 2] <= z_max)
        points = points[z_mask]
        print(f"  Z值过滤后: {len(points):,} 点 (范围: [{z_min:.2f}, {z_max:.2f}])")
    
    # 点数限制（用于大数据集的处理效率）
    if max_points and len(points) > max_points:
        # 随机采样
        indices = np.random.choice(len(points), max_points, replace=False)
        points = points[indices]
        print(f"  随机采样后: {len(points):,} 点")
    
    return points

def process_point_cloud_pair_enhanced(source_points, target_points, pair_name, output_dir, config):
    """
    使用增强策略处理一对点云的配准和误差分析
    """
    print(f"\n{'='*60}")
    print(f"处理点云对: {pair_name} (增强策略)")
    print(f"{'='*60}")
    
    # 创建点云对象
    source_pcd = o3d.geometry.PointCloud()
    source_pcd.points = o3d.utility.Vector3dVector(source_points)
    
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    
    print(f"源点云: {len(source_points):,} 点")
    print(f"目标点云: {len(target_points):,} 点")
    
    # 执行增强的配准
    print(f"\n--- 执行增强ICP配准 ---")
    start_time = time.time()
    
    trans_init = np.eye(4)
    transformation = icp_registration(source_pcd, target_pcd, trans_init,
                                    max_correspondence_distance=config['processing']['icp_max_correspondence_distance'],
                                    use_preprocessing=True,
                                    use_multiscale=True)
    
    registration_time = time.time() - start_time
    print(f"配准完成，耗时: {registration_time:.1f}秒")
    
    # 应用变换
    source_transformed = source_pcd.transform(transformation)
    
    # 保存变换矩阵
    matrix_file = os.path.join(output_dir, f"{pair_name}_transformation_matrix{config['output']['filename_suffix']}.txt")
    np.savetxt(matrix_file, transformation, fmt='%.8f')
    print(f"变换矩阵已保存: {matrix_file}")
    
    # 计算拼接误差（使用增强匹配策略）
    print(f"\n--- 计算拼接误差（增强匹配策略）---")
    start_time = time.time()
    
    error_file = os.path.join(output_dir, f"{pair_name}_stitching_error{config['output']['filename_suffix']}.txt")
    error_stats = calculate_stitching_error(source_transformed, target_pcd,
                                          distance_threshold=config['processing']['distance_threshold'],
                                          output_file=error_file,
                                          remove_outliers=config['processing']['remove_outliers'],
                                          outlier_method=config['processing']['outlier_method'],
                                          use_enhanced_matching=config['processing']['use_enhanced_matching'])
    
    error_time = time.time() - start_time
    print(f"误差分析完成，耗时: {error_time:.1f}秒")
    
    # 分析结果
    if error_stats:
        print(f"\n--- 结果分析 ---")
        mean_z_error = error_stats.get('mean_z_error', float('inf'))
        std_z_error = error_stats.get('std_z_error', float('inf'))
        
        print(f"Z方向误差:")
        print(f"  平均误差: {mean_z_error:.4f} mm")
        print(f"  标准差: {std_z_error:.4f} mm")
        print(f"  最大误差: {error_stats.get('max_z_error', 'N/A'):.4f} mm")
        print(f"  最小误差: {error_stats.get('min_z_error', 'N/A'):.4f} mm")
        
        # 精度统计
        print(f"精度统计:")
        print(f"  Z误差<0.1mm占比: {error_stats.get('z_error_under_0_1_ratio', 0)*100:.1f}%")
        print(f"  Z误差<0.3mm占比: {error_stats.get('z_error_under_0_3_ratio', 0)*100:.1f}%")
        print(f"  平均距离: {error_stats.get('mean_distance', 'N/A'):.4f} mm")
        
        # 目标达成检查
        target_mean_error = 0.1  # 目标平均误差
        target_std_error = 0.1   # 目标标准差
        
        mean_meets_target = mean_z_error <= target_mean_error
        std_meets_target = std_z_error <= target_std_error
        
        print(f"\n目标达成情况:")
        print(f"  平均误差≤0.1mm: {'✅' if mean_meets_target else '❌'} ({mean_z_error:.4f}mm)")
        print(f"  标准差≤0.1mm: {'✅' if std_meets_target else '❌'} ({std_z_error:.4f}mm)")
        
        if mean_meets_target and std_meets_target:
            print(f"  🎉 增强匹配策略成功达到目标精度！")
        else:
            print(f"  ⚠️  需要进一步优化参数")
        
        return {
            'pair_name': pair_name,
            'registration_time': registration_time,
            'error_time': error_time,
            'error_stats': error_stats,
            'target_achieved': mean_meets_target and std_meets_target
        }
    else:
        print("❌ 误差分析失败")
        return None

def main():
    """
    主函数
    """
    print_header()
    
    # 加载增强配置
    config = get_enhanced_config()
    
    # 数据路径
    rail_dir = 'rail'
    output_dir = config['data']['output_dir']
    
    # 创建输出目录
    os.makedirs(output_dir, exist_ok=True)
    
    # 检查数据文件
    data_files = {}
    for color, filename in config['data']['data_files'].items():
        filepath = os.path.join(rail_dir, filename)
        if os.path.exists(filepath):
            data_files[color] = filepath
            print(f"✓ 找到数据文件: {filepath}")
        else:
            print(f"✗ 缺少数据文件: {filepath}")
            return
    
    # 分析数据范围
    print(f"\n📊 分析数据范围...")
    data_ranges = {}
    for color, filepath in data_files.items():
        points = np.loadtxt(filepath)
        if points.shape[1] > 3:
            points = points[:, :3]
        z_min, z_max = points[:, 2].min(), points[:, 2].max()
        data_ranges[color] = (z_min, z_max)
        print(f"  {color}: Z范围 [{z_min:.2f}, {z_max:.2f}], 点数 {len(points):,}")
    
    # 确定公共Z范围
    all_z_mins = [r[0] for r in data_ranges.values()]
    all_z_maxs = [r[1] for r in data_ranges.values()]
    common_z_min = max(all_z_mins)
    common_z_max = min(all_z_maxs)
    
    print(f"\n公共Z范围: [{common_z_min:.2f}, {common_z_max:.2f}]")
    
    # 加载和过滤数据
    print("\n📁 加载和过滤数据...")
    filtered_data = {}
    max_points_per_cloud = config['processing']['max_points_per_cloud']
    
    for color, filepath in data_files.items():
        filtered_data[color] = load_and_filter_pointcloud(
            filepath, 
            z_range=(common_z_min, common_z_max),
            max_points=max_points_per_cloud
        )
    
    # 定义要处理的点云对
    pairs = [
        ('blue', 'green'),
        ('blue', 'red'), 
        ('green', 'red')
    ]
    
    # 处理每对点云
    results = {}
    total_start_time = time.time()
    
    for source_color, target_color in pairs:
        pair_name = f"{source_color}_vs_{target_color}"
        
        if source_color in filtered_data and target_color in filtered_data:
            result = process_point_cloud_pair_enhanced(
                filtered_data[source_color],
                filtered_data[target_color],
                pair_name,
                output_dir,
                config
            )
            if result:
                results[pair_name] = result
        else:
            print(f"❌ 跳过 {pair_name}：数据不完整")
    
    total_time = time.time() - total_start_time
    
    # 生成总结报告
    print(f"\n{'='*80}")
    print("📋 增强匹配策略处理总结")
    print(f"{'='*80}")
    print(f"总处理时间: {total_time:.1f}秒")
    print(f"成功处理: {len(results)}/3 对点云")
    
    successful_pairs = 0
    for pair_name, result in results.items():
        print(f"\n{pair_name}:")
        if result['target_achieved']:
            print(f"  ✅ 达到目标精度")
            successful_pairs += 1
        else:
            print(f"  ❌ 未达到目标精度")
        
        stats = result['error_stats']
        print(f"  平均Z误差: {stats['mean_z_error']:.4f}mm")
        print(f"  Z误差标准差: {stats['std_z_error']:.4f}mm")
        print(f"  匹配点对: {stats['matched_pairs']:,}")
    
    print(f"\n🎯 总体成功率: {successful_pairs}/{len(results)} ({successful_pairs/len(results)*100:.1f}%)")
    
    if successful_pairs == len(results):
        print("🏆 增强匹配策略完全成功！可以替代异常值移除方法。")
    elif successful_pairs > 0:
        print("⚠️  增强匹配策略部分成功，需要进一步优化参数。")
    else:
        print("❌ 增强匹配策略未达到预期效果，需要重新设计算法。")

if __name__ == "__main__":
    main()
