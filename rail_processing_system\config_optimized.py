#!/usr/bin/env python3
"""
优化配置文件 - 专门解决增强匹配策略中的问题
针对blue_vs_green配准困难的情况进行特别优化
"""

# 数据路径配置
DATA_CONFIG = {
    'rail_data_dir': 'rail',
    'output_dir': 'rail_results_optimized',
    'data_files': {
        'blue': 'blue.xyz',
        'green': 'green.xyz',
        'red': 'red.xyz'
    }
}

# 处理参数配置（优化版）
PROCESSING_CONFIG = {
    # 采样参数（增加采样点数）
    'max_points_per_cloud': 80000,  # 增加采样点数
    'sampling_method': 'random',    # 采样方法
    
    # Z值滤波参数
    'use_common_z_range': True,     # 使用公共Z范围
    'z_range_method': 'intersection',
    'manual_z_range': None,
    
    # ICP配准参数（进一步优化）
    'icp_max_correspondence_distance': 0.05,  # 放宽ICP阈值
    'icp_relative_fitness': 1e-6,
    'icp_relative_rmse': 1e-6,
    'icp_max_iteration': 500,                # 大幅增加迭代次数

    # 拼接误差分析参数（优化版）
    'distance_threshold': 0.5,       # 放宽距离阈值
    'error_percentiles': [25, 50, 75, 95],
    'remove_outliers': False,        # 禁用异常值过滤
    'outlier_method': 'iqr',
    'use_enhanced_matching': True,   # 启用增强匹配策略
}

# 算法配置（优化版）
ALGORITHM_CONFIG = {
    # 预处理（调整）
    'remove_outliers': True,
    'outlier_nb_points': 15,        # 减少邻居数，保留更多点
    'outlier_std_ratio': 3.0,       # 放宽标准差比率

    # 滤波（调整）
    'voxel_size': 0.03,            # 增大体素大小，保留更多点
    'statistical_filter': True,
    'radius_filter': False,

    # 配准（优化）
    'use_manual_registration': False,
    'manual_points_count': 4,
    'registration_method': 'multi_scale_icp',
    'use_preprocessing': True,
    'use_multiscale': True,
    'use_z_constraint': True,
}

# 增强匹配策略配置（优化版）
ENHANCED_MATCHING_CONFIG = {
    # 多尺度搜索参数（放宽）
    'knn_search_count': 8,          # 增加K近邻搜索数量
    'max_z_difference_ratio': 3.0,  # 放宽Z方向差异阈值
    'min_neighbor_density': 2,      # 降低最小邻居密度要求
    'density_search_radius_ratio': 2.0,  # 增大密度搜索半径
    
    # 综合得分权重（调整）
    'distance_weight': 0.3,         # 降低距离权重
    'z_error_weight': 0.3,          # 降低Z误差权重
    'density_weight': 0.4,          # 增加密度权重
    
    # 质量控制（放宽）
    'min_match_quality_score': 0.6, # 降低最小匹配质量得分
    'enable_geometric_consistency': True,
    'enable_local_density_filter': True,
}

def get_optimized_config():
    """
    获取优化配置
    """
    return {
        'data': DATA_CONFIG,
        'processing': PROCESSING_CONFIG,
        'algorithm': ALGORITHM_CONFIG,
        'enhanced_matching': ENHANCED_MATCHING_CONFIG
    }

def create_adaptive_enhanced_matching(reference_pcd, target_pcd, distance_threshold=0.5):
    """
    自适应增强匹配策略
    根据点云特征动态调整参数
    """
    import numpy as np
    import open3d as o3d
    
    print(f"  使用自适应增强匹配策略 (距离阈值: {distance_threshold}mm)")
    
    reference_points = np.asarray(reference_pcd.points)
    target_points = np.asarray(target_pcd.points)
    
    # 分析点云密度
    ref_density = len(reference_points) / ((reference_points.max(axis=0) - reference_points.min(axis=0)).prod())
    tar_density = len(target_points) / ((target_points.max(axis=0) - target_points.min(axis=0)).prod())
    
    print(f"    参考点云密度: {ref_density:.2f}, 目标点云密度: {tar_density:.2f}")
    
    # 根据密度调整参数
    if ref_density < 1000 or tar_density < 1000:  # 低密度点云
        knn_count = 10
        min_density = 1
        distance_weight = 0.2
        z_weight = 0.2
        density_weight = 0.6
        print(f"    检测到低密度点云，使用宽松参数")
    else:  # 高密度点云
        knn_count = 5
        min_density = 3
        distance_weight = 0.4
        z_weight = 0.4
        density_weight = 0.2
        print(f"    检测到高密度点云，使用标准参数")
    
    # 构建目标点云的KD树
    target_tree = o3d.geometry.KDTreeFlann(target_pcd)
    
    # 存储匹配结果
    reference_matched = []
    target_matched = []
    distances = []
    z_errors = []
    
    print(f"    执行自适应多尺度最近邻搜索...")
    for i, ref_point in enumerate(reference_points):
        # 搜索k个最近邻
        k = min(knn_count, len(target_points))
        [num_found, indices, distances_squared] = target_tree.search_knn_vector_3d(ref_point, k)
        
        if num_found > 0:
            # 计算所有候选点的距离
            candidate_distances = np.sqrt(distances_squared)
            
            # 筛选距离阈值内的候选点
            valid_candidates = []
            for j in range(num_found):
                if candidate_distances[j] < distance_threshold:
                    target_idx = indices[j]
                    target_point = target_points[target_idx]
                    
                    # 几何一致性检查
                    z_diff = abs(ref_point[2] - target_point[2])
                    if z_diff < distance_threshold * 3:  # 放宽Z方向约束
                        
                        # 局部密度检查
                        [density_count, _, _] = target_tree.search_radius_vector_3d(
                            target_point, distance_threshold * 2.0)  # 增大搜索半径
                        
                        if density_count >= min_density:
                            valid_candidates.append({
                                'target_idx': target_idx,
                                'target_point': target_point,
                                'distance': candidate_distances[j],
                                'z_error': z_diff,
                                'density': density_count
                            })
            
            # 选择最佳匹配
            if valid_candidates:
                best_candidate = None
                best_score = float('inf')
                
                for candidate in valid_candidates:
                    # 自适应得分计算
                    distance_score = candidate['distance'] / distance_threshold
                    z_error_score = candidate['z_error'] / distance_threshold
                    density_score = 1.0 / max(candidate['density'], 1)
                    
                    total_score = (distance_weight * distance_score + 
                                 z_weight * z_error_score + 
                                 density_weight * density_score)
                    
                    if total_score < best_score:
                        best_score = total_score
                        best_candidate = candidate
                
                if best_candidate:
                    reference_matched.append(ref_point)
                    target_matched.append(best_candidate['target_point'])
                    distances.append(best_candidate['distance'])
                    z_errors.append(best_candidate['z_error'])
    
    print(f"    自适应匹配找到 {len(z_errors)} 个高质量匹配点对")
    
    return reference_matched, target_matched, distances, z_errors

if __name__ == "__main__":
    config = get_optimized_config()
    
    print("=" * 60)
    print("优化配置参数")
    print("=" * 60)
    
    for section_name, section_config in config.items():
        print(f"\n[{section_name.upper()}]")
        print("-" * 30)
        for key, value in section_config.items():
            print(f"{key}: {value}")
