#!/usr/bin/env python3
"""
测试增强匹配策略 vs 异常值移除的效果对比
"""

import os
import sys
import time
import numpy as np
import open3d as o3d
from datetime import datetime

# 添加当前目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from srs_stitching_error import calculate_stitching_error
from srs_profile_register_format_xyz import icp_registration
from config import get_config

def load_xyz_data(filename, max_points=None):
    """
    加载XYZ数据
    """
    print(f"加载数据: {filename}")
    points = np.loadtxt(filename)
    
    if points.shape[1] > 3:
        points = points[:, :3]
    
    if max_points and len(points) > max_points:
        indices = np.random.choice(len(points), max_points, replace=False)
        points = points[indices]
    
    print(f"  加载点数: {len(points)}")
    return points

def filter_by_z_range(points, z_range):
    """
    按Z范围过滤点云
    """
    z_min, z_max = z_range
    z_mask = (points[:, 2] >= z_min) & (points[:, 2] <= z_max)
    return points[z_mask]

def test_matching_strategies():
    """
    测试不同匹配策略的效果
    """
    print("=" * 80)
    print("增强匹配策略 vs 异常值移除效果对比测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 加载配置
    config = get_config()
    
    # 数据路径
    rail_dir = 'rail'
    blue_file = os.path.join(rail_dir, 'blue.xyz')
    green_file = os.path.join(rail_dir, 'green.xyz')
    
    # 检查文件是否存在
    if not os.path.exists(blue_file) or not os.path.exists(green_file):
        print("❌ 数据文件不存在，请确保rail目录中有blue.xyz和green.xyz文件")
        return
    
    # 加载数据
    print("\n📁 加载数据...")
    max_points = 50000
    blue_points = load_xyz_data(blue_file, max_points)
    green_points = load_xyz_data(green_file, max_points)
    
    # 确定公共Z范围
    blue_z_range = [blue_points[:, 2].min(), blue_points[:, 2].max()]
    green_z_range = [green_points[:, 2].min(), green_points[:, 2].max()]
    common_z_min = max(blue_z_range[0], green_z_range[0])
    common_z_max = min(blue_z_range[1], green_z_range[1])
    common_z_range = [common_z_min, common_z_max]
    
    print(f"公共Z范围: [{common_z_min:.2f}, {common_z_max:.2f}]")
    
    # 过滤数据
    blue_filtered = filter_by_z_range(blue_points, common_z_range)
    green_filtered = filter_by_z_range(green_points, common_z_range)
    
    print(f"过滤后点数 - Blue: {len(blue_filtered)}, Green: {len(green_filtered)}")
    
    # 创建点云对象
    blue_pcd = o3d.geometry.PointCloud()
    blue_pcd.points = o3d.utility.Vector3dVector(blue_filtered)
    
    green_pcd = o3d.geometry.PointCloud()
    green_pcd.points = o3d.utility.Vector3dVector(green_filtered)
    
    # 执行配准
    print("\n🔧 执行ICP配准...")
    trans_init = np.eye(4)
    start_time = time.time()
    
    transformation = icp_registration(blue_pcd, green_pcd, trans_init,
                                    max_correspondence_distance=0.03,
                                    use_preprocessing=True,
                                    use_multiscale=True)
    
    registration_time = time.time() - start_time
    print(f"配准完成，耗时: {registration_time:.2f}秒")
    
    # 应用变换
    blue_transformed = blue_pcd.transform(transformation)
    
    # 测试1: 传统方法（异常值移除）
    print("\n" + "="*60)
    print("🔍 测试1: 传统方法（异常值移除）")
    print("="*60)
    
    start_time = time.time()
    error_stats_traditional = calculate_stitching_error(
        blue_transformed, green_pcd,
        distance_threshold=0.3,
        remove_outliers=True,
        outlier_method='iqr',
        use_enhanced_matching=False
    )
    traditional_time = time.time() - start_time
    
    # 测试2: 增强匹配策略（无异常值移除）
    print("\n" + "="*60)
    print("🚀 测试2: 增强匹配策略（无异常值移除）")
    print("="*60)
    
    start_time = time.time()
    error_stats_enhanced = calculate_stitching_error(
        blue_transformed, green_pcd,
        distance_threshold=0.3,
        remove_outliers=False,
        outlier_method='iqr',
        use_enhanced_matching=True
    )
    enhanced_time = time.time() - start_time
    
    # 结果对比
    print("\n" + "="*80)
    print("📊 结果对比分析")
    print("="*80)
    
    def print_stats(name, stats, processing_time):
        if stats:
            print(f"\n{name}:")
            print(f"  处理时间: {processing_time:.3f}秒")
            print(f"  匹配点对: {stats['matched_pairs']:,}")
            print(f"  原始点对: {stats['original_pairs']:,}")
            print(f"  移除异常值: {stats['outliers_removed']}")
            print(f"  平均Z误差: {stats['mean_z_error']:.4f} mm")
            print(f"  Z误差标准差: {stats['std_z_error']:.4f} mm")
            print(f"  最大Z误差: {stats['max_z_error']:.4f} mm")
            print(f"  Z误差<0.1mm占比: {stats['z_error_under_0_1_ratio']:.1%}")
            print(f"  Z误差<0.3mm占比: {stats['z_error_under_0_3_ratio']:.1%}")
            print(f"  平均距离: {stats['mean_distance']:.4f} mm")
            
            # 检查是否达到目标（平均误差和标准差都在0.1mm以内）
            target_achieved = (stats['mean_z_error'] <= 0.1 and 
                             stats['std_z_error'] <= 0.1)
            print(f"  目标达成: {'✅ 是' if target_achieved else '❌ 否'}")
            return target_achieved
        else:
            print(f"\n{name}: ❌ 计算失败")
            return False
    
    traditional_achieved = print_stats("传统方法（异常值移除）", error_stats_traditional, traditional_time)
    enhanced_achieved = print_stats("增强匹配策略", error_stats_enhanced, enhanced_time)
    
    # 综合评估
    print("\n" + "="*80)
    print("🎯 综合评估")
    print("="*80)
    
    if enhanced_achieved and traditional_achieved:
        print("✅ 两种方法都达到了目标精度（平均误差和标准差都在0.1mm以内）")
        
        # 比较性能
        if error_stats_enhanced and error_stats_traditional:
            enhanced_mean = error_stats_enhanced['mean_z_error']
            traditional_mean = error_stats_traditional['mean_z_error']
            enhanced_std = error_stats_enhanced['std_z_error']
            traditional_std = error_stats_traditional['std_z_error']
            
            print(f"\n性能对比:")
            print(f"  平均误差改善: {((traditional_mean - enhanced_mean) / traditional_mean * 100):+.1f}%")
            print(f"  标准差改善: {((traditional_std - enhanced_std) / traditional_std * 100):+.1f}%")
            print(f"  处理时间对比: {enhanced_time / traditional_time:.2f}x")
            
            if enhanced_mean <= traditional_mean and enhanced_std <= traditional_std:
                print("🏆 增强匹配策略在精度上优于或等于传统方法")
            else:
                print("⚠️  增强匹配策略在某些指标上略逊于传统方法")
                
    elif enhanced_achieved:
        print("🎉 增强匹配策略成功达到目标，可以替代异常值移除！")
    elif traditional_achieved:
        print("⚠️  只有传统方法达到目标，增强匹配策略需要进一步优化")
    else:
        print("❌ 两种方法都未达到目标，需要进一步优化配准算法")
    
    # 建议
    print("\n💡 优化建议:")
    if enhanced_achieved:
        print("  ✅ 可以在生产环境中使用增强匹配策略替代异常值移除")
        print("  ✅ 增强匹配策略提供了更稳定和可预测的结果")
        print("  ✅ 减少了对异常值检测参数调优的依赖")
    else:
        print("  🔧 可以尝试调整增强匹配的参数：")
        print("     - 减小距离阈值")
        print("     - 增加密度检查的邻居数量")
        print("     - 调整综合得分的权重")
        print("  🔧 或者结合两种方法：先用增强匹配，再用轻度异常值过滤")

if __name__ == "__main__":
    test_matching_strategies()
