'''
深瑞视相机拼接误差
主要是比较x-z

'''
# 结合get_profile_sick_calitarget.py程序， 编写代码，输入是两个轮廓点云，以其中一个轮廓为基准轮廓， 计算基准轮廓每个点距离另外
# 一个轮廓最近的点， 并筛选出小于0.8的点对， 对筛选出的点对比较z方向的误差，统计出误差的最大，最小，平均误差
# 两个点云距离小于0.8的相邻点， 比较两者z方向的误差

import open3d as o3d
import numpy as np
import matplotlib.pyplot as plt
import logging
import os
import datetime

# 解决中文显示问题
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']  # 用来正常显示中文标签
plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号

def remove_outliers_from_errors(z_errors, distances, method='iqr', factor=1.5):
    """
    从误差数据中去除异常值

    参数:
    z_errors: z方向误差数组
    distances: 对应的距离数组
    method: 异常值检测方法 ('iqr', 'zscore', 'percentile')
    factor: 异常值检测因子

    返回:
    filtered_z_errors, filtered_distances: 过滤后的误差和距离数组
    """
    z_errors = np.array(z_errors)
    distances = np.array(distances)

    if len(z_errors) == 0:
        return z_errors, distances

    if method == 'iqr':
        # 使用四分位距方法
        Q1 = np.percentile(z_errors, 25)
        Q3 = np.percentile(z_errors, 75)
        IQR = Q3 - Q1
        lower_bound = Q1 - factor * IQR
        upper_bound = Q3 + factor * IQR
        mask = (z_errors >= lower_bound) & (z_errors <= upper_bound)

    elif method == 'zscore':
        # 使用Z-score方法
        z_scores = np.abs((z_errors - np.mean(z_errors)) / np.std(z_errors))
        mask = z_scores < factor

    elif method == 'percentile':
        # 使用百分位数方法
        lower_percentile = (100 - 95) / 2  # 2.5%
        upper_percentile = 100 - lower_percentile  # 97.5%
        lower_bound = np.percentile(z_errors, lower_percentile)
        upper_bound = np.percentile(z_errors, upper_percentile)
        mask = (z_errors >= lower_bound) & (z_errors <= upper_bound)

    else:
        mask = np.ones(len(z_errors), dtype=bool)

    filtered_z_errors = z_errors[mask]
    filtered_distances = distances[mask]

    removed_count = len(z_errors) - len(filtered_z_errors)
    if removed_count > 0:
        print(f"  异常值检测: 移除了 {removed_count} 个异常点 ({removed_count/len(z_errors)*100:.1f}%)")

    return filtered_z_errors, filtered_distances


def calculate_stitching_error(reference_pcd, target_pcd, distance_threshold=0.5,
                            output_file=None, remove_outliers=True, outlier_method='iqr'):
    """
    计算两个轮廓点云的拼接误差（改进版）

    参数:
    reference_pcd: 基准轮廓点云 (o3d.geometry.PointCloud)
    target_pcd: 目标轮廓点云 (o3d.geometry.PointCloud)
    distance_threshold: 距离阈值，默认0.5mm（更严格）
    output_file: 输出文件路径，如果为None则不保存文件
    remove_outliers: 是否去除异常值
    outlier_method: 异常值检测方法

    返回:
    error_stats: 包含误差统计信息的字典
    """

    print(f"计算拼接误差 (距离阈值: {distance_threshold}mm)")

    # 构建目标点云的KD树用于快速搜索
    target_tree = o3d.geometry.KDTreeFlann(target_pcd)

    reference_points = np.asarray(reference_pcd.points)
    target_points = np.asarray(target_pcd.points)

    # 存储筛选出的点对
    reference_matched = []
    target_matched = []
    distances = []
    z_errors = []

    print(f"  搜索最近邻点对...")
    # 对基准轮廓中的每个点找最近邻
    for i, ref_point in enumerate(reference_points):
        # 在目标点云中搜索最近邻点
        [k, idx, distance_squared] = target_tree.search_knn_vector_3d(ref_point, 1)

        if k > 0:
            distance = np.sqrt(distance_squared[0])

            # 如果距离小于阈值，则保留这个点对
            if distance < distance_threshold:
                target_idx = idx[0]
                target_point = target_points[target_idx]

                reference_matched.append(ref_point)
                target_matched.append(target_point)
                distances.append(distance)

                # 计算z方向误差
                z_error = abs(ref_point[2] - target_point[2])
                z_errors.append(z_error)

    print(f"  找到 {len(z_errors)} 个匹配点对")

    # 转换为numpy数组便于计算
    z_errors = np.array(z_errors)
    distances = np.array(distances)

    # 异常值检测和过滤
    if remove_outliers and len(z_errors) > 10:  # 至少需要10个点才进行异常值检测
        z_errors_filtered, distances_filtered = remove_outliers_from_errors(
            z_errors, distances, method=outlier_method, factor=1.5
        )
    else:
        z_errors_filtered = z_errors
        distances_filtered = distances

    # 计算Z误差小于0.1mm和0.3mm的占比
    z_errors_under_0_1 = z_errors_filtered[z_errors_filtered < 0.1]
    z_errors_under_0_3 = z_errors_filtered[z_errors_filtered < 0.3]
    z_error_under_0_1_ratio = len(z_errors_under_0_1) / len(z_errors_filtered) if len(z_errors_filtered) > 0 else 0
    z_error_under_0_3_ratio = len(z_errors_under_0_3) / len(z_errors_filtered) if len(z_errors_filtered) > 0 else 0

    # 计算百分位数
    if len(z_errors_filtered) > 0:
        percentiles = [25, 50, 75, 95]
        z_error_percentiles = np.percentile(z_errors_filtered, percentiles)

    # 计算误差统计
    if len(z_errors_filtered) > 0:
        error_stats = {
            'matched_pairs': len(z_errors_filtered),
            'original_pairs': len(z_errors),
            'outliers_removed': len(z_errors) - len(z_errors_filtered),
            'max_z_error': np.max(z_errors_filtered),
            'min_z_error': np.min(z_errors_filtered),
            'mean_z_error': np.mean(z_errors_filtered),
            'std_z_error': np.std(z_errors_filtered),
            'max_distance': np.max(distances_filtered),
            'mean_distance': np.mean(distances_filtered),
            'z_error_under_0_1_ratio': z_error_under_0_1_ratio,
            'z_error_under_0_3_ratio': z_error_under_0_3_ratio,
            'z_error_percentiles': z_error_percentiles
        }
        
        # 打印统计结果

        result_text = []
        result_text.append(f"原始匹配点对: {error_stats['original_pairs']}")
        if error_stats['outliers_removed'] > 0:
            result_text.append(f"移除异常值: {error_stats['outliers_removed']} ({error_stats['outliers_removed']/error_stats['original_pairs']*100:.1f}%)")
        result_text.append(f"有效匹配点对: {error_stats['matched_pairs']}")
        result_text.append(f"Z方向误差统计:")
        result_text.append(f"  最大误差: {error_stats['max_z_error']:.4f} mm")
        result_text.append(f"  最小误差: {error_stats['min_z_error']:.4f} mm")
        result_text.append(f"  平均误差: {error_stats['mean_z_error']:.4f} mm")
        result_text.append(f"  标准差: {error_stats['std_z_error']:.4f} mm")
        result_text.append(f"距离统计:")
        result_text.append(f"  最大距离: {error_stats['max_distance']:.4f} mm")
        result_text.append(f"  平均距离: {error_stats['mean_distance']:.4f} mm")
        result_text.append(f"Z误差小于0.1mm的占比: {error_stats['z_error_under_0_1_ratio']:.2%}")
        result_text.append(f"Z误差小于0.3mm的占比: {error_stats['z_error_under_0_3_ratio']:.2%}")
        result_text.append(f"Z误差百分位数:")
        for i, percentile in enumerate(error_stats['z_error_percentiles']):
            result_text.append(f"  {percentiles[i]}%: {percentile:.4f} mm")
        
        # 打印到控制台
        for line in result_text:
            print(line)
        
        # 保存到文件
        if output_file:
            try:

                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write("深瑞视相机拼接误差分析结果\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"分析时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"基准点云点数: {len(reference_points)}\n")
                    f.write(f"目标点云点数: {len(np.asarray(target_pcd.points))}\n")
                    f.write(f"距离阈值: {distance_threshold} mm\n")
                    f.write("-" * 50 + "\n")
                    for line in result_text:
                        f.write(line + "\n")
                    f.write("-" * 50 + "\n")
                    f.write("详细点对信息:\n")
                    f.write(f"总点对数: {len(z_errors)}\n")
                    if len(z_errors) > 0:
                        f.write(f"Z误差范围: [{np.min(z_errors):.4f}, {np.max(z_errors):.4f}] mm\n")
                        f.write(f"距离范围: [{np.min(distances):.4f}, {np.max(distances):.4f}] mm\n")
                        # 添加百分位数信息
                        f.write(f"Z误差百分位数:\n")
                        f.write(f"  25%: {np.percentile(z_errors, 25):.4f} mm\n")
                        f.write(f"  50%: {np.percentile(z_errors, 50):.4f} mm\n")
                        f.write(f"  75%: {np.percentile(z_errors, 75):.4f} mm\n")   
                        f.write(f"  95%: {np.percentile(z_errors, 95):.4f} mm\n")
                print(f"结果已保存到文件: {output_file}")
            except Exception as e:
                print(f"保存文件时出错: {e}")
        
        # 可视化误差分布
        plt.figure(figsize=(12, 5))
        
        plt.subplot(1, 2, 1)
        plt.hist(z_errors, bins=30, alpha=0.7, color='blue', edgecolor='black')
        plt.xlabel('Z方向误差 (mm)')
        plt.ylabel('频次')
        plt.title('Z方向误差分布')
        plt.grid(True, alpha=0.3)
        
        plt.subplot(1, 2, 2)
        plt.scatter(distances, z_errors, alpha=0.6, s=10)
        plt.xlabel('点对距离 (mm)')
        plt.ylabel('Z方向误差 (mm)')
        plt.title('距离与Z误差关系')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
        
    else:
        error_stats = {
            'matched_pairs': 0,
            'max_z_error': 0,
            'min_z_error': 0,
            'mean_z_error': 0,
            'std_z_error': 0,
            'max_distance': 0,
            'mean_distance': 0
        }
        message = "没有找到距离小于阈值的匹配点对"
        print(message)
        
        # 保存到文件
        if output_file:
            try:

                with open(output_file, 'w', encoding='utf-8') as f:
                    f.write("深瑞视相机拼接误差分析结果\n")
                    f.write("=" * 50 + "\n")
                    f.write(f"分析时间: {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write(f"基准点云点数: {len(reference_points)}\n")
                    f.write(f"目标点云点数: {len(np.asarray(target_pcd.points))}\n")
                    f.write(f"距离阈值: {distance_threshold} mm\n")
                    f.write("-" * 50 + "\n")
                    f.write(message + "\n")
                    f.write("\n建议：\n")
                    f.write("1. 尝试增加距离阈值\n")
                    f.write("2. 检查两个点云的对齐情况\n")
                    f.write("3. 确认点云坐标系是否一致\n")
                print(f"结果已保存到文件: {output_file}")
            except Exception as e:
                print(f"保存文件时出错: {e}")
    
    return error_stats

def visualize_matched_points(reference_pcd, target_pcd, distance_threshold=0.8):
    """
    可视化匹配的点对
    """
    target_tree = o3d.geometry.KDTreeFlann(target_pcd)
    reference_points = np.asarray(reference_pcd.points)
    
    matched_ref_indices = []
    matched_tar_indices = []
    
    # 找出匹配的点
    for i, ref_point in enumerate(reference_points):
        [k, idx, distance_squared] = target_tree.search_knn_vector_3d(ref_point, 1)
        
        if k > 0 and np.sqrt(distance_squared[0]) < distance_threshold:
            matched_ref_indices.append(i)
            matched_tar_indices.append(idx[0])
    
    # 创建匹配点的点云
    if len(matched_ref_indices) > 0:
        matched_ref_pcd = reference_pcd.select_by_index(matched_ref_indices)
        matched_tar_pcd = target_pcd.select_by_index(matched_tar_indices)
        
        # 设置颜色
        matched_ref_pcd.paint_uniform_color([1, 0, 0])  # 红色
        matched_tar_pcd.paint_uniform_color([0, 1, 0])  # 绿色
        
        # 创建未匹配点的点云
        unmatched_ref_pcd = reference_pcd.select_by_index(matched_ref_indices, invert=True)
        unmatched_tar_pcd = target_pcd.select_by_index(matched_tar_indices, invert=True)
        
        unmatched_ref_pcd.paint_uniform_color([0.5, 0.5, 0.5])  # 灰色
        unmatched_tar_pcd.paint_uniform_color([0.3, 0.3, 0.3])  # 深灰色
        
        # 可视化
        o3d.visualization.draw_geometries([matched_ref_pcd, matched_tar_pcd, 
                                          unmatched_ref_pcd, unmatched_tar_pcd],
                                         window_name="匹配点对可视化 (红色-基准匹配点, 绿色-目标匹配点)")

def main():
    """
    主函数示例
    """
    # 示例：加载两个点云文件
    file_index = 1
    reference_filename = f"./20250730/{file_index}/blue_filtered.xyz"
    target_filename = f"./20250730/{file_index}/green_filtered.xyz"

    # 自动生成输出文件名（基于输入文件路径）
    base_path = os.path.dirname(reference_filename)
    ref_name = os.path.splitext(os.path.basename(reference_filename))[0]
    tar_name = os.path.splitext(os.path.basename(target_filename))[0]
    output_filename = os.path.join(base_path, f"stitching_error_{ref_name}_vs_{tar_name}_corrected.txt")

    distance_th = 1.0

    # 加载点云
    reference_points = np.loadtxt(reference_filename)
    target_points = np.loadtxt(target_filename)

    # 动态确定Z值范围（使用数据的实际范围）
    ref_z_min, ref_z_max = np.min(reference_points[:, 2]), np.max(reference_points[:, 2])
    tar_z_min, tar_z_max = np.min(target_points[:, 2]), np.max(target_points[:, 2])

    # 使用重叠区域
    z_min = max(ref_z_min, tar_z_min)
    z_max = min(ref_z_max, tar_z_max)

    print(f"参考点云Z范围: [{ref_z_min:.2f}, {ref_z_max:.2f}]")
    print(f"目标点云Z范围: [{tar_z_min:.2f}, {tar_z_max:.2f}]")
    print(f"使用重叠Z范围: [{z_min:.2f}, {z_max:.2f}]")

    # 过滤基准点云
    z_mask_ref = (reference_points[:, 2] >= z_min) & (reference_points[:, 2] <= z_max)
    reference_points = reference_points[z_mask_ref]

    # 过滤目标点云
    z_mask_tar = (target_points[:, 2] >= z_min) & (target_points[:, 2] <= z_max)
    target_points = target_points[z_mask_tar]
    
    print(f"过滤后的基准点云点数: {len(reference_points)}")
    print(f"过滤后的目标点云点数: {len(target_points)}")
    
    reference_pcd = o3d.geometry.PointCloud()
    reference_pcd.points = o3d.utility.Vector3dVector(reference_points)
    
    target_pcd = o3d.geometry.PointCloud()
    target_pcd.points = o3d.utility.Vector3dVector(target_points)
    
    # # 计算拼接误差并保存结果
    error_stats = calculate_stitching_error(reference_pcd, target_pcd, 
                                          distance_threshold=distance_th, 
                                          output_file=output_filename)
    
    # # 可视化匹配点对
    visualize_matched_points(reference_pcd, target_pcd, distance_threshold=distance_th)
    
    print("请在主函数中指定点云文件路径")

if __name__ == "__main__":
    main()



